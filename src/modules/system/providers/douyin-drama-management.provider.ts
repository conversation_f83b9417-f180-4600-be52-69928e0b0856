import { DY_APP_ID } from '@/app.config'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { Injectable, Logger } from '@nestjs/common'
import { AxiosInstance, AxiosResponse } from 'axios'
import instance from './axios.instance'
import { DouyinClientProvider } from './douyin-client.provider'
import {
  AlbumInfo,
  API_ENDPOINTS,
  BatchData,
  CreateDramaData,
  CreateDramaRequest,
  DetailData,
  DouyinResponse,
  DramaFetchRequest,
  DramaFetchResponse,
  DramaOnlineData,
  DramaOnlineOperation,
  DramaOnlineQueryData,
  DramaOnlineRequest,
  DramaQueryType,
  DramaReviewData,
  DramaReviewRequest,
  EditDramaData,
  EditDramaRequest,
  EpisodeInfo,
  ImageUploadData,
  ResourceType,
  SingleData,
  VideoMeta,
  VideoUploadData,
} from './interfaces/douyin-drama-management.interface'
import { get } from 'lodash-unified'

@Injectable()
export class DouyinDramaManagementProvider {
  private readonly instance: AxiosInstance

  constructor(
    private readonly douyinClient: DouyinClientProvider,
    private readonly logger: Logger,
  ) {
    this.instance = instance
  }

  // ====================== 资源上传 ======================

  /**
   * 上传图片到抖音
   * @param url 图片URL
   * @param format 图片格式 (可选)
   * @returns 图片的开放ID
   * @throws {BizException} 当上传失败时抛出异常
   */
  async uploadImage(url: string): Promise<string> {
    console.log(new Error() instanceof BizException)

    const requestData = {
      resource_type: ResourceType.IMAGE,
      ma_app_id: DY_APP_ID,
      image_meta: { url },
    }
    const response = await this.makeRequest<ImageUploadData>(API_ENDPOINTS.RESOURCE_UPLOAD, requestData)

    const openPicId = get(response.data.data, 'image_result.open_pic_id')
    if (!openPicId) {
      throw new BizException(ErrorCodeEnum.DOUYIN_UPLOAD_FAILED)
    }

    this.logger.log(`图片上传成功: ${url} -> ${openPicId}`)
    return openPicId
  }

  /**
   * 上传视频到抖音
   * @param videoMeta 视频元数据
   * @returns 视频的开放ID
   * @throws {BizException} 当上传失败时抛出异常
   */
  async uploadVideo(videoMeta: VideoMeta): Promise<string> {
    const requestData = {
      resource_type: ResourceType.VIDEO,
      ma_app_id: DY_APP_ID,
      video_meta: videoMeta,
    }

    const response = await this.makeRequest<VideoUploadData>(API_ENDPOINTS.RESOURCE_UPLOAD, requestData)

    const openVideoId = get(response.data.data, 'video_result.open_video_id')
    if (!openVideoId) {
      throw new BizException(ErrorCodeEnum.DOUYIN_UPLOAD_FAILED)
    }

    this.logger.log(`视频上传成功: ${videoMeta.title} -> ${openVideoId}`)
    return openVideoId
  }

  // ====================== 短剧管理 ======================

  /**
   * 创建短剧
   * @param albumInfo 短剧信息
   * @returns 短剧ID
   * @throws {BizException} 当创建失败时抛出异常
   */
  async createDrama(albumInfo: CreateDramaRequest['album_info']): Promise<string> {
    const requestData: CreateDramaRequest = {
      ma_app_id: DY_APP_ID,
      album_info: albumInfo,
    }

    const response = await this.makeRequest<CreateDramaData>(API_ENDPOINTS.CREATE_DRAMA, requestData)

    const albumId = response.data.data.album_id
    this.logger.log(`短剧创建成功: ${albumInfo.title} -> ${albumId}`)

    return albumId
  }

  /**
   * 编辑短剧
   * @param albumId 短剧ID
   * @param albumInfo 短剧信息（可选）
   * @param episodeInfoList 剧集信息列表（可选）
   * @returns 编辑短剧响应数据
   * @throws {BizException} 当编辑失败时抛出异常
   */
  async editDrama(albumId: string, albumInfo?: AlbumInfo, episodeInfoList?: EpisodeInfo[]): Promise<EditDramaData> {
    const requestData: EditDramaRequest = {
      ma_app_id: DY_APP_ID,
      album_id: albumId,
    }

    if (albumInfo) {
      requestData.album_info = albumInfo
    }

    if (episodeInfoList && episodeInfoList.length > 0) {
      requestData.episode_info_list = episodeInfoList
    }

    this.logger.log(
      `编辑短剧: albumId=${albumId}, 更新短剧信息=${!!albumInfo}, 更新分集=${episodeInfoList?.length || 0}集`,
    )

    const response = await this.makeRequest<EditDramaData>(API_ENDPOINTS.EDIT_DRAMA, requestData)
    const result = response.data.data

    this.logger.log(
      `短剧编辑成功: albumId=${albumId}, 版本=${result.version}, 分集映射=${JSON.stringify(result.episode_id_map)}`,
    )

    return result
  }

  // ====================== 送审管理 ======================

  /**
   * 短剧送审
   * @param albumId 短剧ID
   * @returns 送审响应数据
   * @throws {BizException} 当送审失败时抛出异常
   */
  async submitDramaForReview(albumId: number | string): Promise<DramaReviewData> {
    // 检验短剧ID是否存在
    if (!albumId) {
      throw new BizException(ErrorCodeEnum.VALIDATE_FAILED)
    }

    const requestData: DramaReviewRequest = {
      ma_app_id: DY_APP_ID,
      album_id: albumId,
    }

    this.logger.log(`短剧送审: albumId=${albumId}`)

    const response = await this.makeRequest<DouyinResponse<DramaReviewData>>(API_ENDPOINTS.DRAMA_REVIEW, requestData)

    // 提取出DramaReviewData
    const result = response.data.data.data
    this.logger.log(`短剧送审成功: albumId=${albumId}, version=${result.version}`)

    return result
  }

  // ====================== 上线管理 ======================

  /**
   * 短剧上线
   * @param albumId 短剧ID
   * @param version 上线版本号
   * @returns 上线响应数据
   * @throws {BizException} 当上线失败时抛出异常
   */
  async onlineDrama(albumId: number | string, version: number): Promise<DramaOnlineData> {
    if (!albumId || !version) {
      throw new BizException(ErrorCodeEnum.VALIDATE_FAILED)
    }

    const requestData: DramaOnlineRequest = {
      ma_app_id: DY_APP_ID,
      album_id: albumId,
      operate: DramaOnlineOperation.ONLINE,
      version,
    }

    this.logger.log(`短剧上线: albumId=${albumId}, version=${version}`)

    const response = await this.makeRequest<DramaOnlineData>(API_ENDPOINTS.DRAMA_ONLINE, requestData)

    const result = response.data.data
    this.logger.log(`短剧上线成功: albumId=${albumId}, version=${result.version}`)

    return result
  }

  /**
   * 短剧下线
   * @param albumId 短剧ID
   * @param version 下线版本号
   * @returns 下线响应数据
   * @throws {BizException} 当下线失败时抛出异常
   */
  async offlineDrama(albumId: number | string, version: number): Promise<DramaOnlineData> {
    if (!albumId || !version) {
      throw new BizException(ErrorCodeEnum.VALIDATE_FAILED)
    }

    const requestData: DramaOnlineRequest = {
      ma_app_id: DY_APP_ID,
      album_id: albumId,
      operate: DramaOnlineOperation.OFFLINE,
      version,
    }

    this.logger.log(`短剧下线: albumId=${albumId}, version=${version}`)

    const response = await this.makeRequest<DramaOnlineData>(API_ENDPOINTS.DRAMA_ONLINE, requestData)

    const result = response.data.data
    this.logger.log(`短剧下线成功: albumId=${albumId}, version=${result.version}`)

    return result
  }

  /**
   * 查询短剧线上状态
   * @param albumId 短剧ID
   * @returns 线上状态查询响应数据
   * @throws {BizException} 当查询失败时抛出异常
   */
  async queryDramaOnlineStatus(albumId: number | string): Promise<DramaOnlineQueryData> {
    if (!albumId) {
      throw new BizException(ErrorCodeEnum.VALIDATE_FAILED)
    }

    const requestData: DramaOnlineRequest = {
      ma_app_id: DY_APP_ID,
      album_id: albumId,
      operate: DramaOnlineOperation.QUERY,
    }

    this.logger.log(`查询短剧线上状态: albumId=${albumId}`)

    const response = await this.makeRequest<DramaOnlineQueryData>(API_ENDPOINTS.DRAMA_FETCH, requestData)

    const result = response.data.data
    if (result == null) {
      throw new BizException(ErrorCodeEnum.DOUYIN_DRAMA_NOT_FOUND)
    }

    this.logger.log(
      `查询短剧线上状态成功: albumId=${albumId}, version=${result?.version}, status=${result?.playlet_status}`,
    )

    return result
  }

  // ====================== 查询管理 ======================

  /**
   * 批量查询所有短剧
   * @param offset 分页偏移量
   * @param limit 分页限制数量
   * @returns 批量查询响应数据
   * @throws {BizException} 当查询失败时抛出异常
   */
  async fetchAllDramas(offset: number = 0, limit: number = 20): Promise<BatchData> {
    const requestData: DramaFetchRequest = {
      ma_app_id: DY_APP_ID,
      query_type: DramaQueryType.BATCH,
      batch_query: {
        offset,
        limit,
      },
    }

    this.logger.log(`批量查询短剧: offset=${offset}, limit=${limit}`)

    const response = await this.makeRequest<DramaFetchResponse>(API_ENDPOINTS.DRAMA_FETCH, requestData)

    const result = response.data.data.batch_data
    if (!result) {
      throw new BizException(ErrorCodeEnum.DOUYIN_API_ERROR)
    }

    this.logger.log(`批量查询短剧成功: 返回${result.album_info_list.length}个短剧，总数${result.total}`)

    return result
  }

  /**
   * 查询单个短剧所有版本信息
   * @param albumId 短剧ID
   * @param offset 分页偏移量
   * @param limit 分页限制数量
   * @returns 单个短剧查询响应数据
   * @throws {BizException} 当查询失败时抛出异常
   */
  async fetchDramaVersions(albumId: number | string, offset: number = 0, limit: number = 20): Promise<SingleData> {
    if (!albumId) {
      throw new BizException(ErrorCodeEnum.VALIDATE_FAILED)
    }

    const requestData: DramaFetchRequest = {
      ma_app_id: DY_APP_ID,
      query_type: DramaQueryType.SINGLE,
      single_query: {
        album_id: Number(albumId),
        offset,
        limit,
      },
    }

    this.logger.log(`查询短剧版本: albumId=${albumId}, offset=${offset}, limit=${limit}`)

    const response = await this.makeRequest<DramaFetchResponse>(API_ENDPOINTS.DRAMA_FETCH, requestData)

    const result = response.data.data.single_data
    if (!result) {
      throw new BizException(ErrorCodeEnum.DOUYIN_API_ERROR)
    }

    this.logger.log(
      `查询短剧版本成功: albumId=${albumId}, 返回${result.album_info_list.length}个版本，总数${result.total}`,
    )

    return result
  }

  /**
   * 查询单个短剧某版本的剧集详情
   * @param albumId 短剧ID
   * @param version 版本号
   * @param offset 分页偏移量
   * @param limit 分页限制数量
   * @returns 短剧详情查询响应数据
   * @throws {BizException} 当查询失败时抛出异常
   */
  async fetchDramaEpisodes(
    albumId: number | string,
    version: number,
    offset: number = 0,
    limit: number = 20,
  ): Promise<DetailData> {
    if (!albumId || version == null) {
      throw new BizException(ErrorCodeEnum.VALIDATE_FAILED)
    }

    const requestData: DramaFetchRequest = {
      ma_app_id: DY_APP_ID,
      query_type: DramaQueryType.DETAIL,
      detail_query: {
        album_id: Number(albumId),
        version,
        offset,
        limit,
      },
    }

    this.logger.log(`查询短剧剧集: albumId=${albumId}, version=${version}, offset=${offset}, limit=${limit}`)

    const response = await this.makeRequest<DramaFetchResponse>(API_ENDPOINTS.DRAMA_FETCH, requestData)

    const result = response.data.data.detail_data
    if (!result) {
      throw new BizException(ErrorCodeEnum.DOUYIN_API_ERROR)
    }

    this.logger.log(
      `查询短剧剧集成功: albumId=${albumId}, version=${version}, 返回${result.episode_info_list?.length || 0}个剧集，总数${result.total}`,
    )

    return result
  }

  /**
   * 全量查询所有短剧（自动分页）
   * @returns 所有短剧信息
   * @throws {BizException} 当查询失败时抛出异常
   */
  async fetchAllDramasComplete(): Promise<BatchData> {
    this.logger.log('开始全量查询所有短剧')

    let offset = 0
    const limit = 50 // 每页50个
    let allAlbums: BatchData['album_info_list'] = []
    let total = 0

    do {
      const result = await this.fetchAllDramas(offset, limit)
      allAlbums = allAlbums.concat(result.album_info_list)
      total = result.total
      offset += limit

      this.logger.log(`已获取${allAlbums.length}/${total}个短剧`)
    } while (allAlbums.length < total)

    this.logger.log(`全量查询完成: 共获取${allAlbums.length}个短剧`)

    return {
      total,
      album_info_list: allAlbums,
    }
  }

  /**
   * 发送请求
   * @param endpoint 端点
   * @param data 请求数据
   * @returns 响应数据
   * @throws {BizException} 当请求失败时抛出异常
   */
  private async makeRequest<T>(endpoint: string, data: any): Promise<AxiosResponse<DouyinResponse<T>>> {
    const accessToken = await this.getClientToken()

    // 打印请求信息
    // this.logger.log(`📤 发送请求到: ${endpoint}`)
    // this.logger.log(`📤 请求数据: ${JSON.stringify(data, null, 2)}`)
    // this.logger.log(`📤 请求头: access-token=${accessToken}`)

    const response = await this.instance.post<DouyinResponse<T>>(endpoint, data, {
      headers: {
        'access-token': accessToken,
      },
    })

    // 直接判断返回的 错误msg ，有时候响应成功了，code还是会返回东西
    if (!!response.data.err_msg && response.data.err_msg !== '') {
      this.logger.error(`${endpoint} 请求失败: ${JSON.stringify(data)} 报错信息: ${response?.data?.err_msg}`)
      throw new BizException(ErrorCodeEnum.DOUYIN_API_ERROR)
    }

    return response
  }

  /**
   * 获取客户端访问令牌
   */
  private async getClientToken(): Promise<string> {
    try {
      const tokenInfo = await this.douyinClient.getOAuthClientToken()
      if (!tokenInfo) {
        throw new BizException(ErrorCodeEnum.DOUYIN_CLIENT_TOKEN_FAILED)
      }
      const accessToken = tokenInfo.accessToken
      if (!accessToken) {
        throw new BizException(ErrorCodeEnum.DOUYIN_CLIENT_TOKEN_FAILED)
      }
      return accessToken
    } catch (error) {
      this.logger.error('Failed to get client token', error instanceof Error ? error.stack : undefined)
      throw new BizException(ErrorCodeEnum.DOUYIN_CLIENT_TOKEN_FAILED)
    }
  }
}
