import { Injectable, Logger } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { dramas, episodes, videoUploads } from '@/common/drizzle/schema'
import { VideoUploadStatus } from '@/common/drizzle/schema/video-uploads'
import { and, eq, sql } from 'drizzle-orm'
import { LogicDelete } from '@/constants/system.constants'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { getUserContext } from '@/context/user.context'
import { Role } from '@/modules/auth/interfaces/jwt-payload.interface'
import { CreateEpisodeRequest } from '../requests/episode.request'
import { DouyinDramaManagementProvider } from '../providers/douyin-drama-management.provider'
import { CreateEpisodeResponse, EpisodeResponse } from '../responses/episode.response'
import { toResponse } from '@/common/utils/transform.util'

@Injectable()
export class DramaEpisodeService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly logger: Logger,
    private readonly douyinDramaManagementProvider: DouyinDramaManagementProvider,
  ) {}

  /**
   * 创建分集
   * @param dramaId 短剧ID
   * @param createRequest 创建请求
   * @returns 创建结果
   */
  async createEpisode(dramaId: string, createRequest: CreateEpisodeRequest): Promise<CreateEpisodeResponse> {
    this.logger.log(`开始创建分集: dramaId=${dramaId}, seq=${createRequest.seq}, title=${createRequest.title}`)

    // 1. 前置验证
    await this.validateCreateEpisode(dramaId, createRequest)

    // 2. 获取短剧信息
    const drama = await this.getDramaForEdit(dramaId)

    // 3. 使用分布式锁防止并发
    // TODO: 看有没有必要使用Redis分布式锁
    // const lockKey = `drama:${dramaId}:episode:lock`

    try {
      // 4. 验证视频资源并上传封面图片
      const { coverOpenPicId } = await this.prepareResources(createRequest)

      // 5. 事务操作
      const result = await this.drizzle.db.transaction(async (tx) => {
        // 5.1 创建本地分集记录
        const newEpisodeData = {
          dramaId,
          title: createRequest.title,
          seq: createRequest.seq,
          coverUrl: createRequest.coverUrl,
          coverOpenPicId,
          openVideoId: createRequest.openVideoId,
          videoUrl: createRequest.videoUrl,
          videoUploadStatus: 1, // 上传成功
          description: createRequest.description || null,
          duration: createRequest.duration || null,
          fileSize: createRequest.fileSize || null,
          isFree: createRequest.isFree || 0,
          price: createRequest.price?.toString() || '0.00',
          version: 0, // 临时设置，后面会更新
        }

        const [newEpisodeId] = await tx.insert(episodes).values(newEpisodeData).$returningId()

        // 5.2 查询该短剧下所有分集
        const allEpisodes = await tx.query.episodes.findMany({
          where: and(eq(episodes.dramaId, dramaId), eq(episodes.isDeleted, LogicDelete.NotDeleted)),
          orderBy: (table, { asc }) => [asc(table.seq)],
        })

        // 5.3 调用抖音编辑短剧API
        if (!drama.albumId) {
          throw new BizException(ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN)
        }

        // 5.3 检查所有分集是否都有抖音资源
        const episodesWithoutResources = allEpisodes.filter((ep) => !ep.coverOpenPicId || !ep.openVideoId)
        if (episodesWithoutResources.length > 0) {
          const missingSeqs = episodesWithoutResources.map((ep) => ep.seq).join(', ')
          this.logger.error(`分集缺少抖音资源: 第${missingSeqs}集`)
          throw new BizException(ErrorCodeEnum.EPISODE_MISSING_DOUYIN_RESOURCES)
        }

        // 5.4 调用抖音编辑短剧API
        const douyinResult = await this.douyinDramaManagementProvider.editDrama(
          drama.albumId,
          undefined, // 不更新短剧信息
          allEpisodes.map((ep) => ({
            title: ep.title,
            seq: ep.seq,
            cover_list: [ep.coverOpenPicId!], // 使用非空断言，因为已经检查过了
            open_video_id: ep.openVideoId!,
          })),
        )
        // version转字符串
        const versionStr = douyinResult.version.toString()

        // 5.5 安全的批量更新：使用参数化查询防止SQL注入
        // TODO 这里有点争议，因为每次上传新的分集也会把该短剧下的其他分集也一起传过去，
        // 这样就导致episode_id_map会返回所有分集信息，那么在这里面更新version字段就没有意义了，这个字段没用
        if (Object.keys(douyinResult.episode_id_map).length > 0) {
          const updates = Object.entries(douyinResult.episode_id_map)
          const seqList = updates.map(([seq]) => parseInt(seq))

          // 构建安全的 CASE WHEN 语句，使用参数化查询
          const caseWhenParts = updates.map(
            ([seq, episodeId]) => sql`WHEN seq = ${parseInt(seq)} THEN ${episodeId.toString()}`,
          )

          // 使用安全的参数化查询执行批量更新
          // 设置抖音返回的剧集id和version

          await tx.execute(sql`
            UPDATE episode
            SET
              douyin_episode_id = CASE ${sql.join(caseWhenParts, sql` `)} END,
              version = ${versionStr}
            WHERE dramaId = ${dramaId}
              AND seq IN (${sql.join(
                seqList.map((seq) => sql`${seq}`),
                sql`, `,
              )})
          `)
        } else {
          // 按道理不可能没有分集映射，但是如果没有分集映射，只更新版本号
          await tx.update(episodes).set({ version: douyinResult.version }).where(eq(episodes.dramaId, dramaId))
        }

        // 5.6 更新短剧版本
        await tx.execute(sql`
  UPDATE dramas 
  SET 
    version = ${versionStr},
    updated_at = ${new Date()}
  WHERE id = ${dramaId}
`)


        // 返回新创建的分集（更新后的）
        const updatedEpisode = await tx.query.episodes.findFirst({
          where: eq(episodes.id, newEpisodeId.id),
        })

        // 如果不存在则报错
        if (!updatedEpisode) {
          throw new BizException(ErrorCodeEnum.EPISODE_CREATE_FAILED)
        }

        return {
          episode: updatedEpisode,
          newVersion: douyinResult.version,
        }
      })

      this.logger.log(
        `分集创建成功: episodeId=${result.episode.id}, seq=${result.episode.seq}, newVersion=${result.newVersion}`,
      )

      return {
        episode: toResponse(EpisodeResponse, result.episode),
        newVersion: result.newVersion,
        message: `分集创建成功，短剧版本更新为 ${result.newVersion}`,
      }
    } finally {
      // TODO: 释放分布式锁
    }
  }

  /**
   * 验证创建分集的前置条件
   */
  private async validateCreateEpisode(dramaId: string, createRequest: CreateEpisodeRequest) {
    // 1. 验证短剧是否存在
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, dramaId), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 2. 权限验证：创作者只能操作自己的短剧，管理员可以操作所有短剧
    const user = getUserContext()
    if (user.role !== Role.ADMIN && drama.authorId !== user.id) {
      throw new BizException(ErrorCodeEnum.FORBIDDEN)
    }

    // 3. 验证短剧是否正在审核中
    if (drama.douyinAuditStatus === 'reviewing') {
      throw new BizException(ErrorCodeEnum.DRAMA_IN_REVIEW)
    }

    // 4. 验证分集集数是否超过短剧总集数
    if (drama.seqNum && createRequest.seq > drama.seqNum) {
      throw new BizException(ErrorCodeEnum.EPISODE_SEQ_EXCEED_LIMIT)
    }

    // 5. 验证分集集数是否重复
    const existingEpisode = await this.drizzle.db.query.episodes.findFirst({
      where: and(
        eq(episodes.dramaId, dramaId),
        eq(episodes.seq, createRequest.seq),
        eq(episodes.isDeleted, LogicDelete.NotDeleted),
      ),
    })

    if (existingEpisode) {
      throw new BizException(ErrorCodeEnum.EPISODE_SEQ_DUPLICATE)
    }

    // 6. 验证短剧是否已在抖音创建
    if (!drama.albumId) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN)
    }
  }

  /**
   * 获取用于编辑的短剧信息
   */
  private async getDramaForEdit(dramaId: string) {
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: and(eq(dramas.id, dramaId), eq(dramas.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    return drama
  }

  /**
   * 准备抖音资源：验证视频资源并上传封面图片
   */
  private async prepareResources(createRequest: CreateEpisodeRequest) {
    this.logger.log(`准备抖音资源: openVideoId=${createRequest.openVideoId}, coverUrl=${createRequest.coverUrl}`)

    // // 1. 验证视频资源是否已上传成功
    // if (!createRequest.openVideoId) {
    //   throw new BizException(ErrorCodeEnum.EPISODE_MISSING_DOUYIN_RESOURCES)
    // }
    //
    // // 2. 验证视频是否在上传记录中且状态为成功
    // const videoUpload = await this.drizzle.db.query.videoUploads.findFirst({
    //   where: and(
    //     eq(videoUploads.openVideoId, createRequest.openVideoId),
    //     eq(videoUploads.status, VideoUploadStatus.SUCCESS),
    //     eq(videoUploads.isDeleted, LogicDelete.NotDeleted),
    //   ),
    // })
    //
    // if (!videoUpload) {
    //   throw new BizException(ErrorCodeEnum.EPISODE_MISSING_DOUYIN_RESOURCES)
    // }

    // 3. 同步上传封面图片
    const coverOpenPicId = await this.douyinDramaManagementProvider.uploadImage(createRequest.coverUrl)

    this.logger.log(`抖音资源准备完成: coverOpenPicId=${coverOpenPicId}, openVideoId=${createRequest.openVideoId}`)

    return { coverOpenPicId }
  }

  findAll() {
    // TODO: 实现获取分集列表的业务逻辑
    return {}
  }

  findOne() {
    // TODO: 实现获取分集详情的业务逻辑
    return {}
  }

  update() {
    // TODO: 实现更新分集的业务逻辑
    return {}
  }

  delete() {
    // TODO: 实现删除分集的业务逻辑
    return {}
  }
}
