import { Injectable, Logger } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { dramas, episodes, videoUploads } from '@/common/drizzle/schema'
import { VideoUploadStatus } from '@/common/drizzle/schema/video-uploads'
import { eq, and } from 'drizzle-orm'
import { LogicDelete } from '@/constants/system.constants'
import {
  DouyinAuditStatus,
  AlbumAuditCallbackData,
  EpisodeAuditCallbackData,
  UploadVideoCallbackData,
  PostReviewCallbackData,
} from '../interfaces/douyin-callback.interface'
import { DouyinAuditRecordService } from './douyin-audit-record.service'

// ====================== 类型定义 ======================

/** 包含短剧信息的剧集数据 */
interface EpisodeWithDrama {
  id: string
  title: string
  drama?: {
    id: string
    title: string
    albumId: string | null
  } | null
}

@Injectable()
export class DouyinCallbackService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly auditRecordService: DouyinAuditRecordService,
    private readonly logger: Logger,
  ) {}

  /**
   * 处理剧审核回调
   */
  async handleAlbumAudit(auditRecord: any) {
    const callbackData = (auditRecord as { record: { rawCallbackData: AlbumAuditCallbackData } }).record.rawCallbackData
    const { album_id, version, audit_status, scope_list, audit_msg } = callbackData

    this.logger.log(`处理剧审核回调: albumId=${album_id}, version=${version}, status=${audit_status}`)

    // 映射审核状态
    const douyinAuditStatus = audit_status === DouyinAuditStatus.APPROVED ? 'approved' : 'rejected'
    const platformAuditStatus = audit_status === DouyinAuditStatus.APPROVED ? 'approved' : 'rejected'

    // 更新短剧状态
    const updateData: {
      version: number
      douyinAuditStatus: string
      platformAuditStatus: string
      auditMsg: string | null
      scopeList: string
      lastAuditTime: Date
      auditSuccessVersion?: number
    } = {
      version: version,
      douyinAuditStatus: douyinAuditStatus,
      platformAuditStatus: platformAuditStatus,
      auditMsg: audit_msg || null,
      scopeList: JSON.stringify(scope_list),
      lastAuditTime: new Date(),
    }

    // 只有审核通过时才更新auditSuccessVersion
    if (douyinAuditStatus === 'approved') {
      updateData.auditSuccessVersion = version
    }

    await this.drizzle.db.update(dramas).set(updateData).where(eq(dramas.albumId, album_id.toString()))

    // 标记记录为已处理
    await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)

    this.logger.log(`剧审核处理完成: albumId=${album_id}, status=${douyinAuditStatus}`)
  }

  /**
   * 处理集审核回调
   */
  async handleEpisodeAudit(auditRecord: any) {
    const callbackData = (auditRecord as { record: { rawCallbackData: EpisodeAuditCallbackData } }).record
      .rawCallbackData
    const { episode_id, version, audit_status } = callbackData
    // 移除未使用的变量: album_id, scope_list, audit_msg

    this.logger.log(`处理集审核回调: episodeId=${episode_id}, version=${version}, status=${audit_status}`)

    // TODO: 实现集审核逻辑
    // 这里需要更新 episodes 表的审核状态
    // 由于当前主要关注剧审核，集审核逻辑可以后续完善

    // 标记记录为已处理
    await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)

    this.logger.log(`集审核处理完成: episodeId=${episode_id}`)
  }

  /**
   * 处理视频上传结果回调
   *
   * 回调说明：
   * - 如果 use_dy_cloud=true，会有两次回调
   * - 第一次：视频上传成功回调 (success=true) - 仅记录日志，不更新状态
   * - 第二次：同步抖音云成功回调 (success_to_dy_cloud=true) - 更新最终状态
   * - 最终以同步抖音云回调为准，第一次回调可以忽略
   */
  async handleVideoUpload(auditRecord: any) {
    const callbackData = (auditRecord as { record: { rawCallbackData: UploadVideoCallbackData } }).record
      .rawCallbackData
    const { open_video_id, success, success_to_dy_cloud, dy_cloud_id } = callbackData

    this.logger.log(
      `处理视频上传回调: videoId=${open_video_id}, success=${success}, successToDyCloud=${success_to_dy_cloud}, dyCloudId=${dy_cloud_id}`,
    )

    try {
      // 1. 查找视频上传记录（优先）
      const videoUpload = await this.drizzle.db.query.videoUploads.findFirst({
        where: and(
          eq(videoUploads.openVideoId, open_video_id),
          eq(videoUploads.isDeleted, LogicDelete.NotDeleted)
        ),
      })

      // 2. 查找对应的剧集记录（如果存在）
      const episode = await this.drizzle.db.query.episodes.findFirst({
        where: and(eq(episodes.openVideoId, open_video_id), eq(episodes.isDeleted, LogicDelete.NotDeleted)),
        with: {
          drama: {
            columns: {
              id: true,
              title: true,
              albumId: true,
            },
          },
        },
      })

      if (!videoUpload && !episode) {
        this.logger.warn(`未找到对应的视频上传记录或剧集记录: videoId=${open_video_id}`)
        // 即使找不到记录，也要标记为已处理，避免重复处理
        await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)
        return
      }

      if (videoUpload) {
        this.logger.log(`找到视频上传记录: uploadId=${videoUpload.id}, title=${videoUpload.title}`)
      }

      if (episode) {
        this.logger.log(`找到剧集记录: episodeId=${episode.id}, title=${episode.title}`)
      }

      // 3. 判断回调类型和处理逻辑
      if (success_to_dy_cloud !== undefined) {
        // 这是同步抖音云的回调（第二次回调）- 最终状态，以此为准
        await this.handleDyCloudSyncCallback(videoUpload, episode, success_to_dy_cloud, dy_cloud_id, open_video_id)
      } else if (success !== undefined) {
        // 这是视频上传的回调（第一次回调）- 仅记录日志，不更新状态
        this.handleVideoUploadCallback(videoUpload, episode, success, open_video_id)
      }

      // 4. 标记记录为已处理
      await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)

      this.logger.log(`视频上传回调处理完成: videoId=${open_video_id}, uploadId=${videoUpload?.id}, episodeId=${episode?.id}`)
    } catch (error) {
      this.logger.error(`处理视频上传回调失败: videoId=${open_video_id}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        callbackData,
      })

      // TODO 这块逻辑待确认  即使处理失败，也要标记为已处理，避免无限重试
      await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)
      throw error
    }
  }

  /**
   * 处理视频上传成功回调（第一次回调）
   * 仅记录日志，不更新状态，因为最终以抖音云同步回调为准
   * @private
   */
  private handleVideoUploadCallback(
    videoUpload: any,
    episode: any,
    success: boolean,
    openVideoId: string
  ) {
    this.logger.log(`收到视频上传回调（第一次）: success=${success}, openVideoId=${openVideoId}`)

    if (videoUpload) {
      this.logger.log(`对应视频上传记录: uploadId=${videoUpload.id}, title=${videoUpload.title}`)
    }

    if (episode) {
      this.logger.log(`对应剧集记录: episodeId=${episode.id}, title=${episode.title}`)
    }

    if (success) {
      this.logger.log(`视频上传到抖音成功，等待同步到抖音云: videoId=${openVideoId}`)
    } else {
      this.logger.warn(`视频上传到抖音失败: videoId=${openVideoId}`)
    }
  }

  /**
   * 处理抖音云同步回调（第二次回调）
   * 这是最终状态，决定视频是否可用
   * @private
   */
  private async handleDyCloudSyncCallback(
    videoUpload: any,
    episode: any,
    successToDyCloud: boolean,
    dyCloudId: string | undefined,
    openVideoId: string,
  ) {
    this.logger.log(
      `处理抖音云同步回调: successToDyCloud=${successToDyCloud}, dyCloudId=${dyCloudId}, openVideoId=${openVideoId}`,
    )

    if (successToDyCloud && dyCloudId) {
      // 抖音云同步成功，视频可以正常使用

      // 1. 更新视频上传表状态为最终成功
      if (videoUpload) {
        await this.drizzle.db
          .update(videoUploads)
          .set({
            status: VideoUploadStatus.SUCCESS,
            callbackData: JSON.stringify({
              successToDyCloud,
              dyCloudId,
              openVideoId,
              timestamp: new Date()
            }),
            updatedAt: new Date(),
          })
          .where(eq(videoUploads.id, videoUpload.id))

        this.logger.log(`视频上传表最终状态更新为成功: uploadId=${videoUpload.id}`)
      }

      // 2. 如果存在对应的剧集，也更新剧集表状态
      if (episode) {
        await this.drizzle.db
          .update(episodes)
          .set({
            videoUploadStatus: 1, // 1: 上传成功（可以正常播放）
            updatedAt: new Date(),
          })
          .where(eq(episodes.id, episode.id))

        this.logger.log(`剧集视频状态更新为可播放: episodeId=${episode.id}`)
      }

      // 记录成功日志，包含关键信息
      this.logger.log(`视频完整上传流程完成`, {
        uploadId: videoUpload?.id || null,
        episodeId: episode?.id || null,
        episodeTitle: episode?.title || null,
        dramaId: episode?.drama?.id || null,
        dramaTitle: episode?.drama?.title || null,
        albumId: episode?.drama?.albumId || null,
        openVideoId,
        dyCloudId,
        operation: 'video_upload_complete',
        timestamp: new Date().toISOString(),
      })
    } else {
      // 抖音云同步失败
      this.logger.error(`抖音云同步失败: videoId=${openVideoId}`)

      // 1. 更新视频上传表状态为失败
      if (videoUpload) {
        await this.drizzle.db
          .update(videoUploads)
          .set({
            status: VideoUploadStatus.FAILED,
            failureReason: '抖音云同步失败',
            callbackData: JSON.stringify({
              successToDyCloud,
              dyCloudId,
              openVideoId,
              timestamp: new Date()
            }),
            updatedAt: new Date(),
          })
          .where(eq(videoUploads.id, videoUpload.id))

        this.logger.log(`视频上传表状态更新为失败: uploadId=${videoUpload.id}`)
      }

      // 2. 如果存在对应的剧集，也更新剧集表状态
      if (episode) {
        await this.drizzle.db
          .update(episodes)
          .set({
            videoUploadStatus: 0, // 0: 上传失败（同步失败，不可播放）
            updatedAt: new Date(),
          })
          .where(eq(episodes.id, episode.id))

        this.logger.log(`剧集视频状态更新为不可播放: episodeId=${episode.id}`)
      }

      // 记录详细的失败信息
      this.logger.error(`抖音云同步失败详情`, {
        episodeId: episode.id,
        episodeTitle: episode.title,
        dramaId: episode.drama?.id || null,
        dramaTitle: episode.drama?.title || null,
        albumId: episode.drama?.albumId || null,
        openVideoId,
        dyCloudId,
        operation: 'dy_cloud_sync_failed',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 处理后置审核回调
   */
  async handlePostReview(auditRecord: any) {
    const callbackData = (auditRecord as { record: { rawCallbackData: PostReviewCallbackData } }).record.rawCallbackData
    const { review_id, review_type, review_result } = callbackData

    this.logger.log(`处理后置审核回调: reviewId=${review_id}, type=${review_type}, result=${review_result}`)

    // TODO: 实现后置审核处理逻辑
    // 根据 review_type 和 review_result 更新相应的状态

    // 标记记录为已处理
    await this.auditRecordService.markAsProcessed((auditRecord as { id: string }).id)

    this.logger.log(`后置审核处理完成: reviewId=${review_id}`)
  }

  /**
   * 批量处理未处理的记录（补偿机制）
   */
  async processUnhandledRecords() {
    this.logger.log('开始处理未处理的审核记录')

    const unprocessedRecords = await this.auditRecordService.findUnprocessedRecords()

    for (const record of unprocessedRecords) {
      // 根据回调类型触发相应的处理
      switch (record.callbackType) {
        case 'album_audit':
          await this.handleAlbumAudit({ id: record.id, record })
          break
        case 'episode_audit':
          await this.handleEpisodeAudit({ id: record.id, record })
          break
        case 'upload_video':
          await this.handleVideoUpload({ id: record.id, record })
          break
        case 'post_review':
          await this.handlePostReview({ id: record.id, record })
          break
      }
    }

    this.logger.log(`未处理记录处理完成，共处理 ${unprocessedRecords.length} 条记录`)
  }
}
